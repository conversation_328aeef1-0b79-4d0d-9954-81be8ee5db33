.formContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.progressBar {
  position: relative;
  margin-bottom: 3rem;
  padding: 0 2rem;
}

.progressSteps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.progressStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.progressStep.active .stepNumber {
  background: #3b82f6;
  color: white;
}

.progressStep.completed .stepNumber {
  background: #10b981;
  color: white;
}

.stepLabel {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progressStep.active .stepLabel,
.progressStep.completed .stepLabel {
  color: #374151;
}

.progressLine {
  position: absolute;
  top: 20px;
  left: 2rem;
  right: 2rem;
  height: 2px;
  background: #10b981;
  transition: width 0.3s ease;
  z-index: 1;
}

.progressLine::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: #e5e7eb;
  z-index: -1;
}

.stepContent {
  margin-bottom: 2rem;
}

.stepContent h3 {
  margin-bottom: 1.5rem;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formGroup input.error,
.formGroup select.error,
.formGroup textarea.error {
  border-color: #ef4444;
}

.formGroup input.error:focus,
.formGroup select.error:focus,
.formGroup textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formRow.triple {
  grid-template-columns: 1fr 1fr 1fr;
}

.errorText {
  display: block;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.checkboxGroup {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
}

.checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.formActions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.primaryButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 120px;
}

.primaryButton:hover:not(:disabled) {
  background: #2563eb;
}

.primaryButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.secondaryButton {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.secondaryButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.secondaryButton:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .formContainer {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .progressBar {
    padding: 0 1rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .formRow.triple {
    grid-template-columns: 1fr;
  }
  
  .checkboxGroup {
    grid-template-columns: 1fr;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .stepLabel {
    display: none;
  }
}

@media (max-width: 480px) {
  .formContainer {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .progressSteps {
    justify-content: center;
    gap: 2rem;
  }
  
  .stepNumber {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
}
