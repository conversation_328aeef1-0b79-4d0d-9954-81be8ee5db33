/* Blue and White Theme Variables */
.Headerroot {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #ffffff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: rgba(37, 99, 235, 0.1);
  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header Styles - Completely transparent design */
.header {
  background: transparent;
  backdrop-filter: none;
  border: none;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Header when scrolled - stays transparent */
.header.scrolled {
  /* background: transparent;
  backdrop-filter: none; */
    transform: translateY(-100%);

}

/* Header for non-home pages - blue background with white text */
.header.nonHomePage {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  backdrop-filter: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(37, 99, 235, 0.1);
}

.header.nonHomePage.scrolled {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo Styles */
.logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logoLink {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.scrolled .logoLink {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.nonHomePage .logoLink {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.logoLink:hover {
  transform: translateY(-1px);
}

.logoText {
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: 1;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  text-transform: uppercase;
}

.logoSubtext {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
}

.nonHomePage .logoSubtext {
  color: rgba(0, 0, 0, 0.9);
}

/* Navigation Styles */
.nav {
  display: flex;
  align-items: center;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
  align-items: center;
}

.navItem {
  position: relative;
  display: flex;
  align-items: center;
}

.navLink,
.navButton {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.scrolled .navLink,
.scrolled .navButton {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.nonHomePage .navLink,
.nonHomePage .navButton {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.navLink:hover,
.navButton:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.scrolled .navLink:hover,
.scrolled .navButton:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.nonHomePage .navLink:hover,
.nonHomePage .navButton:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);
}

.navLink::after,
.navButton::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.scrolled .navLink::after,
.scrolled .navButton::after {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.nonHomePage .navLink::after,
.nonHomePage .navButton::after {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.navLink:hover::after,
.navButton:hover::after,
.navButton.active::after {
  width: 80%;
}

/* Dropdown Styles */
.dropdownContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdownIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.dropdownIcon.rotated {
  transform: rotate(180deg);
  color: rgba(255, 255, 255, 0.9);
}

.nonHomePage .dropdownIcon {
  color: rgba(255, 255, 255, 0.8);
}

.nonHomePage .dropdownIcon.rotated {
  color: var(--cast-stone-white);
}

.loadingIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--cast-stone-white);
  border: 1px solid rgba(74, 55, 40, 0.1);
  border-radius: 8px;
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  min-width: 280px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: var(--transition-smooth);
  animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  backdrop-filter: blur(10px);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
}

.dropdownList {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdownItem {
  position: relative;
}

.dropdownLink {
  display: block;
  padding: 0.75rem 1.25rem;
  color: var(--cast-stone-dark-text);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.dropdownLink:hover {
  background: rgba(37, 99, 235, 0.04);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-blue);
  transform: translateX(2px);
}

/* Sub-dropdown Styles */
.subDropdownList {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(37, 99, 235, 0.02);
  border-top: 1px solid rgba(37, 99, 235, 0.08);
}

.subDropdownItem {
  position: relative;
}

.subDropdownLink {
  display: block;
  padding: 0.6rem 1.25rem 0.6rem 2rem;
  color: var(--cast-stone-gray-text);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
  position: relative;
}

.subDropdownLink::before {
  content: '→  ';
  position: absolute;
  left: 0.75rem;
  color: var(--cast-stone-gray-text);
  font-size: 0.7rem;
  transition: var(--transition-fast);
}

.subDropdownLink:hover {
  background: rgba(37, 99, 235, 0.06);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  transform: translateX(2px);
}

.subDropdownLink:hover::before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

/* Sub-sub-dropdown Styles (Level 3) */
.subSubDropdownList {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(37, 99, 235, 0.04);
  border-top: 1px solid rgba(37, 99, 235, 0.12);
}

.subSubDropdownItem {
  position: relative;
}

.subSubDropdownLink {
  display: block;
  padding: 0.5rem 1.25rem 0.5rem 2.5rem;
  color: var(--cast-stone-gray-text);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 6px solid transparent;
  position: relative;
}

.subSubDropdownLink::before {
  content: '⤷  ';
  position: absolute;
  left: 1.5rem;
  color: var(--cast-stone-gray-text);
  font-size: 0.65rem;
  transition: var(--transition-fast);
}

.subSubDropdownLink:hover {
  background: rgba(37, 99, 235, 0.08);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  transform: translateX(2px);
}

.subSubDropdownLink:hover::before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

/* Cart Styles */
.cartContainer {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.cartLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--transition-smooth);
  position: relative;
}

.nonHomePage .cartLink {
  color: var(--cast-stone-white);
}

.cartLink:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.nonHomePage .cartLink:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--cast-stone-white);
}

.cartIconWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cartBadge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc2626;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: cartBadgeAppear 0.3s ease-out;
}

@keyframes cartBadgeAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .navList {
    gap: 0;
  }

  .navLink,
  .navButton {
    padding: 1.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
    height: 70px;
  }

  .logoText {
    font-size: 1.75rem;
  }

  .logoSubtext {
    font-size: 0.7rem;
  }

  .navList {
    gap: 0;
  }

  .navLink,
  .navButton {
    padding: 1.25rem 0.75rem;
    font-size: 0.85rem;
  }

  .dropdown {
    min-width: 200px;
  }
}

@media (max-width: 640px) {
  .nav {
    display: none; /* Will implement mobile menu in future */
  }

  .container {
    justify-content: space-between;
  }
}
